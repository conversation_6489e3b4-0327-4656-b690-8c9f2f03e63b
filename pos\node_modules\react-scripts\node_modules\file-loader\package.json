{"name": "file-loader", "version": "6.1.1", "description": "A file loader module for webpack", "license": "MIT", "repository": "webpack-contrib/file-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/file-loader", "bugs": "https://github.com/webpack-contrib/file-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.5.2", "cross-env": "^7.0.2", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-import": "^2.22.1", "husky": "^4.3.0", "jest": "^26.5.2", "lint-staged": "^10.4.0", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.1.2", "standard-version": "^9.0.0", "url-loader": "^4.1.0", "webpack": "^4.44.2"}, "keywords": ["webpack"]}