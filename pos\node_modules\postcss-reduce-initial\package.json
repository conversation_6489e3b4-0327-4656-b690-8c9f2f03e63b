{"name": "postcss-reduce-initial", "version": "5.1.0", "description": "Reduce initial definitions to the actual initial value, where possible.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"browserslist": "^4.16.6", "caniuse-api": "^3.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"@types/caniuse-api": "^3.0.2", "postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "scripts": {"acquire": "node ./src/script/acquire.mjs"}, "readme": "# [postcss][postcss]-reduce-initial\n\n> Reduce `initial` definitions to the _actual_ initial value, where possible.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-reduce-initial) do:\n\n```\nnpm install postcss-reduce-initial --save\n```\n\n## Examples\n\nSee the [data](data) for more conversions. This data is courtesy\nof Mozilla.\n\n### Convert `initial` values\n\nWhen the `initial` keyword is longer than the property value, it will\nbe converted:\n\n#### Input\n\n```css\nh1 {\n  min-width: initial;\n}\n```\n\n#### Output\n\n```css\nh1 {\n  min-width: 0;\n}\n```\n\n### Convert values back to `initial`\n\nWhen the `initial` value is smaller than the property value, it will\nbe converted:\n\n#### Input\n\n```css\nh1 {\n  transform-box: border-box;\n}\n```\n\n#### Output\n\n```css\nh1 {\n  transform-box: initial;\n}\n```\n\nThis conversion is only applied when you supply a browsers list that all support\nthe `initial` keyword; it's worth noting that Internet Explorer has no support.\n\n## API\n\n### reduceInitial([options])\n\n#### options\n\n##### ignore\n\nType: `Array<String>`\nDefault: `undefined`\n\nIt contains the Array of properties that will be ignored while reducing its value to initial.\nExample : `{ ignore : [\"min-height\"] }`\n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nThis program uses a list of CSS properties derived from data maintained my the MDN team at Mozilla and licensed under the [CC0 1.0 Universal Public Domain Dedication](https://creativecommons.org/publicdomain/zero/1.0/).\n\nMIT © [Ben Briggs](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}