{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "version": "3.0.1", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^2.0", "brotzka/laravel-dotenv-editor": "^2.1", "doctrine/dbal": "^3.5", "guzzlehttp/guzzle": "^7.2", "infyomlabs/adminlte-templates": "^6.0", "infyomlabs/laravel-generator": "^6.0", "laravel/framework": "^10.23", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "picqer/php-barcode-generator": "^2.2", "prettus/l5-repository": "^2.9", "spatie/laravel-json-api-paginate": "^1.12", "spatie/laravel-medialibrary": "^10.7", "spatie/laravel-permission": "^5.8", "spatie/laravel-query-builder": "^5.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.3", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}