{"name": "postcss-colormin", "version": "4.0.3", "description": "Minify colors in your CSS files with PostCSS.", "main": "dist/index.js", "files": ["dist", "LICENSE-MIT"], "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel-node ./src/generate.js && babel src --out-dir dist --ignore /__tests__/,src/generate.js"}, "keywords": ["color", "colors", "compression", "css", "minify", "postcss", "postcss-plugin"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "css-color-names": "0.0.4", "write-file": "^1.0.0"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"browserslist": "^4.0.0", "color": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}}