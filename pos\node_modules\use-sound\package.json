{"name": "use-sound", "author": "<PERSON>", "version": "4.0.1", "module": "dist/use-sound.esm.js", "exports": {"import": "./dist/use-sound.esm.js", "require": "./dist/index.js"}, "repository": "github:joshw<PERSON><PERSON>/use-sound", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test --passWithNoTests", "lint": "tsdx lint", "prepare": "tsdx build", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "files": ["dist"], "dependencies": {"howler": "^2.1.3"}, "devDependencies": {"@babel/core": "^7.8.7", "@storybook/addon-actions": "^5.3.17", "@storybook/addon-knobs": "^5.3.17", "@storybook/addon-links": "^5.3.17", "@storybook/addons": "^5.3.17", "@storybook/react": "^5.3.17", "@types/howler": "2.1.2", "@types/jest": "^25.1.4", "@types/react": "^16.9.23", "@types/react-dom": "^16.9.5", "@typescript-eslint/eslint-plugin": "2.x", "@typescript-eslint/parser": "2.x", "babel-eslint": "10.x", "babel-loader": "^8.0.6", "eslint": "6.x", "eslint-config-react-app": "^5.2.0", "eslint-plugin-flowtype": "4.x", "eslint-plugin-import": "2.x", "eslint-plugin-jsx-a11y": "6.x", "eslint-plugin-react": "7.x", "eslint-plugin-react-hooks": "2.x", "focus-visible": "^5.0.2", "husky": "^4.2.3", "react": "^16.13.0", "react-dom": "^16.13.0", "react-spring": "^8.0.27", "styled-components": "^5.0.1", "ts-loader": "^6.2.1", "tsdx": "^0.12.3", "tslib": "^1.11.1", "typescript": "^3.8.3"}, "peerDependencies": {"react": ">=16.8"}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}}