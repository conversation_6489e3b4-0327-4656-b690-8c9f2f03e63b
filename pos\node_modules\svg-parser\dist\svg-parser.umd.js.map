{"version": 3, "file": "svg-parser.umd.js", "sources": ["../node_modules/locate-character/dist/locate-character.es.js", "../src/index.js"], "sourcesContent": ["function getLocator(source, options) {\n    if (options === void 0) { options = {}; }\n    var offsetLine = options.offsetLine || 0;\n    var offsetColumn = options.offsetColumn || 0;\n    var originalLines = source.split('\\n');\n    var start = 0;\n    var lineRanges = originalLines.map(function (line, i) {\n        var end = start + line.length + 1;\n        var range = { start: start, end: end, line: i };\n        start = end;\n        return range;\n    });\n    var i = 0;\n    function rangeContains(range, index) {\n        return range.start <= index && index < range.end;\n    }\n    function getLocation(range, index) {\n        return { line: offsetLine + range.line, column: offsetColumn + index - range.start, character: index };\n    }\n    function locate(search, startIndex) {\n        if (typeof search === 'string') {\n            search = source.indexOf(search, startIndex || 0);\n        }\n        var range = lineRanges[i];\n        var d = search >= range.end ? 1 : -1;\n        while (range) {\n            if (rangeContains(range, search))\n                return getLocation(range, search);\n            i += d;\n            range = lineRanges[i];\n        }\n    }\n    ;\n    return locate;\n}\nfunction locate(source, search, options) {\n    if (typeof options === 'number') {\n        throw new Error('locate takes a { startIndex, offsetLine, offsetColumn } object as the third argument');\n    }\n    return getLocator(source, options)(search, options && options.startIndex);\n}\n\nexport { getLocator, locate };", "import { locate } from 'locate-character';\n\nconst validNameCharacters = /[a-zA-Z0-9:_-]/;\nconst whitespace = /[\\s\\t\\r\\n]/;\nconst quotemark = /['\"]/;\n\nfunction repeat(str, i) {\n\tlet result = '';\n\twhile (i--) result += str;\n\treturn result;\n}\n\nexport function parse(source) {\n\tlet header = '';\n\tlet stack = [];\n\n\tlet state = metadata;\n\tlet currentElement = null;\n\tlet root = null;\n\n\tfunction error(message) {\n\t\tconst { line, column } = locate(source, i);\n\t\tconst before = source.slice(0, i);\n\t\tconst beforeLine = /(^|\\n).*$/.exec(before)[0].replace(/\\t/g, '  ');\n\t\tconst after = source.slice(i);\n\t\tconst afterLine = /.*(\\n|$)/.exec(after)[0];\n\n\t\tconst snippet = `${beforeLine}${afterLine}\\n${repeat(' ', beforeLine.length)}^`;\n\n\t\tthrow new Error(\n\t\t\t`${message} (${line}:${column}). If this is valid SVG, it's probably a bug in svg-parser. Please raise an issue at https://github.com/Rich-Harris/svg-parser/issues – thanks!\\n\\n${snippet}`\n\t\t);\n\t}\n\n\tfunction metadata() {\n\t\twhile ((i < source.length && source[i] !== '<') || !validNameCharacters.test(source[i + 1])) {\n\t\t\theader += source[i++];\n\t\t}\n\n\t\treturn neutral();\n\t}\n\n\tfunction neutral() {\n\t\tlet text = '';\n\t\twhile (i < source.length && source[i] !== '<') text += source[i++];\n\n\t\tif (/\\S/.test(text)) {\n\t\t\tcurrentElement.children.push({ type: 'text', value: text });\n\t\t}\n\n\t\tif (source[i] === '<') {\n\t\t\treturn tag;\n\t\t}\n\n\t\treturn neutral;\n\t}\n\n\tfunction tag() {\n\t\tconst char = source[i];\n\n\t\tif (char === '?') return neutral; // <?xml...\n\n\t\tif (char === '!') {\n\t\t\tif (source.slice(i + 1, i + 3) === '--') return comment;\n\t\t\tif (source.slice(i + 1, i + 8) === '[CDATA[') return cdata;\n\t\t\tif (/doctype/i.test(source.slice(i + 1, i + 8))) return neutral;\n\t\t}\n\n\t\tif (char === '/') return closingTag;\n\n\t\tconst tagName = getName();\n\n\t\tconst element = {\n\t\t\ttype: 'element',\n\t\t\ttagName,\n\t\t\tproperties: {},\n\t\t\tchildren: []\n\t\t};\n\n\t\tif (currentElement) {\n\t\t\tcurrentElement.children.push(element);\n\t\t} else {\n\t\t\troot = element;\n\t\t}\n\n\t\tlet attribute;\n\t\twhile (i < source.length && (attribute = getAttribute())) {\n\t\t\telement.properties[attribute.name] = attribute.value;\n\t\t}\n\n\t\tlet selfClosing = false;\n\n\t\tif (source[i] === '/') {\n\t\t\ti += 1;\n\t\t\tselfClosing = true;\n\t\t}\n\n\t\tif (source[i] !== '>') {\n\t\t\terror('Expected >');\n\t\t}\n\n\t\tif (!selfClosing) {\n\t\t\tcurrentElement = element;\n\t\t\tstack.push(element);\n\t\t}\n\n\t\treturn neutral;\n\t}\n\n\tfunction comment() {\n\t\tconst index = source.indexOf('-->', i);\n\t\tif (!~index) error('expected -->');\n\n\t\ti = index + 2;\n\t\treturn neutral;\n\t}\n\n\tfunction cdata() {\n\t\tconst index = source.indexOf(']]>', i);\n\t\tif (!~index) error('expected ]]>');\n\n\t\tcurrentElement.children.push(source.slice(i + 7, index));\n\n\t\ti = index + 2;\n\t\treturn neutral;\n\t}\n\n\tfunction closingTag() {\n\t\tconst tagName = getName();\n\n\t\tif (!tagName) error('Expected tag name');\n\n\t\tif (tagName !== currentElement.tagName) {\n\t\t\terror(`Expected closing tag </${tagName}> to match opening tag <${currentElement.tagName}>`);\n\t\t}\n\n\t\tallowSpaces();\n\n\t\tif (source[i] !== '>') {\n\t\t\terror('Expected >');\n\t\t}\n\n\t\tstack.pop();\n\t\tcurrentElement = stack[stack.length - 1];\n\n\t\treturn neutral;\n\t}\n\n\tfunction getName() {\n\t\tlet name = '';\n\t\twhile (i < source.length && validNameCharacters.test(source[i])) name += source[i++];\n\n\t\treturn name;\n\t}\n\n\tfunction getAttribute() {\n\t\tif (!whitespace.test(source[i])) return null;\n\t\tallowSpaces();\n\n\t\tconst name = getName();\n\t\tif (!name) return null;\n\n\t\tlet value = true;\n\n\t\tallowSpaces();\n\t\tif (source[i] === '=') {\n\t\t\ti += 1;\n\t\t\tallowSpaces();\n\n\t\t\tvalue = getAttributeValue();\n\t\t\tif (!isNaN(value) && value.trim() !== '') value = +value; // TODO whitelist numeric attributes?\n\t\t}\n\n\t\treturn { name, value };\n\t}\n\n\tfunction getAttributeValue() {\n\t\treturn quotemark.test(source[i]) ? getQuotedAttributeValue() : getUnquotedAttributeValue();\n\t}\n\n\tfunction getUnquotedAttributeValue() {\n\t\tlet value = '';\n\t\tdo {\n\t\t\tconst char = source[i];\n\t\t\tif (char === ' ' || char === '>' || char === '/') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvalue += char;\n\t\t\ti += 1;\n\t\t} while (i < source.length);\n\n\t\treturn value;\n\t}\n\n\tfunction getQuotedAttributeValue() {\n\t\tconst quotemark = source[i++];\n\n\t\tlet value = '';\n\t\tlet escaped = false;\n\n\t\twhile (i < source.length) {\n\t\t\tconst char = source[i++];\n\t\t\tif (char === quotemark && !escaped) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (char === '\\\\' && !escaped) {\n\t\t\t\tescaped = true;\n\t\t\t}\n\n\t\t\tvalue += escaped ? `\\\\${char}` : char;\n\t\t\tescaped = false;\n\t\t}\n\t}\n\n\tfunction allowSpaces() {\n\t\twhile (i < source.length && whitespace.test(source[i])) i += 1;\n\t}\n\n\tlet i = metadata.length;\n\twhile (i < source.length) {\n\t\tif (!state) error('Unexpected character');\n\t\tstate = state();\n\t\ti += 1;\n\t}\n\n\tif (state !== neutral) {\n\t\terror('Unexpected end of input');\n\t}\n\n\tif (root.tagName === 'svg') root.metadata = header;\n\treturn {\n\t\ttype: 'root',\n\t\tchildren: [root]\n\t};\n}\n"], "names": ["const", "let"], "mappings": ";;;;;;IAAA,SAAS,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;IACrC,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;IAC7C,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;IAC7C,IAAI,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;IACjD,IAAI,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE;IAC1D,QAAQ,IAAI,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,QAAQ,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;IACxD,QAAQ,KAAK,GAAG,GAAG,CAAC;IACpB,QAAQ,OAAO,KAAK,CAAC;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;IACd,IAAI,SAAS,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE;IACzC,QAAQ,OAAO,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;IACzD,KAAK;IACL,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;IACvC,QAAQ,OAAO,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IAC/G,KAAK;IACL,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;IACxC,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;IACxC,YAAY,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,CAAC,CAAC;IAC7D,SAAS;IACT,QAAQ,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAClC,QAAQ,IAAI,CAAC,GAAG,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,QAAQ,OAAO,KAAK,EAAE;IACtB,YAAY,IAAI,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC;IAC5C,gBAAgB,OAAO,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAClD,YAAY,CAAC,IAAI,CAAC,CAAC;IACnB,YAAY,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAClC,SAAS;IACT,KAAK;IAEL,IAAI,OAAO,MAAM,CAAC;IAClB,CAAC;IACD,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;IACzC,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;IACrC,QAAQ,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAC;IAChH,KAAK;IACL,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;IAC9E;;ICtCAA,IAAM,mBAAmB,GAAG,gBAAgB,CAAC;IAC7CA,IAAM,UAAU,GAAG,YAAY,CAAC;IAChCA,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB;IACA,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE;IACxB,CAACC,IAAI,MAAM,GAAG,EAAE,CAAC;IACjB,CAAC,OAAO,CAAC,EAAE,IAAE,MAAM,IAAI,GAAG,GAAC;IAC3B,CAAC,OAAO,MAAM,CAAC;IACf,CAAC;AACD;AACA,IAAO,SAAS,KAAK,CAAC,MAAM,EAAE;IAC9B,CAACA,IAAI,MAAM,GAAG,EAAE,CAAC;IACjB,CAACA,IAAI,KAAK,GAAG,EAAE,CAAC;AAChB;IACA,CAACA,IAAI,KAAK,GAAG,QAAQ,CAAC;IACtB,CAACA,IAAI,cAAc,GAAG,IAAI,CAAC;IAC3B,CAACA,IAAI,IAAI,GAAG,IAAI,CAAC;AACjB;IACA,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE;IACzB,SAAwB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;MAAjC;MAAM,wBAA6B;IAC7C,EAAED,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,EAAEA,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACtE,EAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC,EAAEA,IAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C;IACA,EAAEA,IAAM,OAAO,GAAG,KAAG,aAAa,SAAS,WAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,EAAC,MAAG,CAAC;AAClF;IACA,EAAE,MAAM,IAAI,KAAK;IACjB,IAAM,OAAO,UAAK,IAAI,SAAI,MAAM,2JAAsJ;IACtL,GAAG,CAAC;IACJ,EAAE;AACF;IACA,CAAC,SAAS,QAAQ,GAAG;IACrB,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAC/F,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB,GAAG;AACH;IACA,EAAE,OAAO,OAAO,EAAE,CAAC;IACnB,EAAE;AACF;IACA,CAAC,SAAS,OAAO,GAAG;IACpB,EAAEC,IAAI,IAAI,GAAG,EAAE,CAAC;IAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAE,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,GAAC;AACrE;IACA,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IACvB,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/D,GAAG;AACH;IACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,GAAG,OAAO,GAAG,CAAC;IACd,GAAG;AACH;IACA,EAAE,OAAO,OAAO,CAAC;IACjB,EAAE;AACF;IACA,CAAC,SAAS,GAAG,GAAG;IAChB,EAAED,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB;IACA,EAAE,IAAI,IAAI,KAAK,GAAG,IAAE,OAAO,OAAO,GAAC;AACnC;IACA,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE;IACpB,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAE,OAAO,OAAO,GAAC;IAC3D,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,SAAS,IAAE,OAAO,KAAK,GAAC;IAC9D,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAE,OAAO,OAAO,GAAC;IACnE,GAAG;AACH;IACA,EAAE,IAAI,IAAI,KAAK,GAAG,IAAE,OAAO,UAAU,GAAC;AACtC;IACA,EAAEA,IAAM,OAAO,GAAG,OAAO,EAAE,CAAC;AAC5B;IACA,EAAEA,IAAM,OAAO,GAAG;IAClB,GAAG,IAAI,EAAE,SAAS;IAClB,YAAG,OAAO;IACV,GAAG,UAAU,EAAE,EAAE;IACjB,GAAG,QAAQ,EAAE,EAAE;IACf,GAAG,CAAC;AACJ;IACA,EAAE,IAAI,cAAc,EAAE;IACtB,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,GAAG,MAAM;IACT,GAAG,IAAI,GAAG,OAAO,CAAC;IAClB,GAAG;AACH;IACA,EAAEC,IAAI,SAAS,CAAC;IAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,YAAY,EAAE,CAAC,EAAE;IAC5D,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;IACxD,GAAG;AACH;IACA,EAAEA,IAAI,WAAW,GAAG,KAAK,CAAC;AAC1B;IACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,GAAG,CAAC,IAAI,CAAC,CAAC;IACV,GAAG,WAAW,GAAG,IAAI,CAAC;IACtB,GAAG;AACH;IACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;IACvB,GAAG;AACH;IACA,EAAE,IAAI,CAAC,WAAW,EAAE;IACpB,GAAG,cAAc,GAAG,OAAO,CAAC;IAC5B,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,GAAG;AACH;IACA,EAAE,OAAO,OAAO,CAAC;IACjB,EAAE;AACF;IACA,CAAC,SAAS,OAAO,GAAG;IACpB,EAAED,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACzC,EAAE,IAAI,CAAC,CAAC,KAAK,IAAE,KAAK,CAAC,cAAc,CAAC,GAAC;AACrC;IACA,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAChB,EAAE,OAAO,OAAO,CAAC;IACjB,EAAE;AACF;IACA,CAAC,SAAS,KAAK,GAAG;IAClB,EAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACzC,EAAE,IAAI,CAAC,CAAC,KAAK,IAAE,KAAK,CAAC,cAAc,CAAC,GAAC;AACrC;IACA,EAAE,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3D;IACA,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAChB,EAAE,OAAO,OAAO,CAAC;IACjB,EAAE;AACF;IACA,CAAC,SAAS,UAAU,GAAG;IACvB,EAAEA,IAAM,OAAO,GAAG,OAAO,EAAE,CAAC;AAC5B;IACA,EAAE,IAAI,CAAC,OAAO,IAAE,KAAK,CAAC,mBAAmB,CAAC,GAAC;AAC3C;IACA,EAAE,IAAI,OAAO,KAAK,cAAc,CAAC,OAAO,EAAE;IAC1C,GAAG,KAAK,8BAA2B,OAAO,iCAA2B,cAAc,CAAC,QAAO,QAAI,CAAC;IAChG,GAAG;AACH;IACA,EAAE,WAAW,EAAE,CAAC;AAChB;IACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;IACvB,GAAG;AACH;IACA,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;IACd,EAAE,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C;IACA,EAAE,OAAO,OAAO,CAAC;IACjB,EAAE;AACF;IACA,CAAC,SAAS,OAAO,GAAG;IACpB,EAAEC,IAAI,IAAI,GAAG,EAAE,CAAC;IAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,GAAC;AACvF;IACA,EAAE,OAAO,IAAI,CAAC;IACd,EAAE;AACF;IACA,CAAC,SAAS,YAAY,GAAG;IACzB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,OAAO,IAAI,GAAC;IAC/C,EAAE,WAAW,EAAE,CAAC;AAChB;IACA,EAAED,IAAM,IAAI,GAAG,OAAO,EAAE,CAAC;IACzB,EAAE,IAAI,CAAC,IAAI,IAAE,OAAO,IAAI,GAAC;AACzB;IACA,EAAEC,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;IACA,EAAE,WAAW,EAAE,CAAC;IAChB,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB,GAAG,CAAC,IAAI,CAAC,CAAC;IACV,GAAG,WAAW,EAAE,CAAC;AACjB;IACA,GAAG,KAAK,GAAG,iBAAiB,EAAE,CAAC;IAC/B,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAE,KAAK,GAAG,CAAC,KAAK,GAAC;IAC5D,GAAG;AACH;IACA,EAAE,OAAO,QAAE,IAAI,SAAE,KAAK,EAAE,CAAC;IACzB,EAAE;AACF;IACA,CAAC,SAAS,iBAAiB,GAAG;IAC9B,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,uBAAuB,EAAE,GAAG,yBAAyB,EAAE,CAAC;IAC7F,EAAE;AACF;IACA,CAAC,SAAS,yBAAyB,GAAG;IACtC,EAAEA,IAAI,KAAK,GAAG,EAAE,CAAC;IACjB,EAAE,GAAG;IACL,GAAGD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;IACrD,IAAI,OAAO,KAAK,CAAC;IACjB,IAAI;AACJ;IACA,GAAG,KAAK,IAAI,IAAI,CAAC;IACjB,GAAG,CAAC,IAAI,CAAC,CAAC;IACV,GAAG,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;AAC9B;IACA,EAAE,OAAO,KAAK,CAAC;IACf,EAAE;AACF;IACA,CAAC,SAAS,uBAAuB,GAAG;IACpC,EAAEA,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAChC;IACA,EAAEC,IAAI,KAAK,GAAG,EAAE,CAAC;IACjB,EAAEA,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB;IACA,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;IAC5B,GAAGD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE;IACvC,IAAI,OAAO,KAAK,CAAC;IACjB,IAAI;AACJ;IACA,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;IAClC,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI;AACJ;IACA,GAAG,KAAK,IAAI,OAAO,WAAQ,QAAS,IAAI,CAAC;IACzC,GAAG,OAAO,GAAG,KAAK,CAAC;IACnB,GAAG;IACH,EAAE;AACF;IACA,CAAC,SAAS,WAAW,GAAG;IACxB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,IAAI,CAAC,GAAC;IACjE,EAAE;AACF;IACA,CAACC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;IAC3B,EAAE,IAAI,CAAC,KAAK,IAAE,KAAK,CAAC,sBAAsB,CAAC,GAAC;IAC5C,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC;IAClB,EAAE,CAAC,IAAI,CAAC,CAAC;IACT,EAAE;AACF;IACA,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE;IACxB,EAAE,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACnC,EAAE;AACF;IACA,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAC;IACpD,CAAC,OAAO;IACR,EAAE,IAAI,EAAE,MAAM;IACd,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC;IAClB,EAAE,CAAC;IACH,CAAC;;;;;;;;;;;;"}