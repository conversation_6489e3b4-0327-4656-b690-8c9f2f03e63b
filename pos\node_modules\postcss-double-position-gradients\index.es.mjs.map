{"version": 3, "file": "index.es.mjs", "sources": ["index.js"], "sourcesContent": ["import postcss from 'postcss';\nimport valueParser from 'postcss-values-parser';\n\nexport default postcss.plugin('postcss-double-position-gradients', opts => {\n\tconst preserve = 'preserve' in Object(opts) ? Bo<PERSON>an(opts.preserve) : true;\n\n\treturn root => {\n\t\t// walk every declaration\n\t\troot.walkDecls(decl => {\n\t\t\tconst { value: originalValue } = decl;\n\n\t\t\t// if the declaration value contains a gradient\n\t\t\tif (gradientFunctionRegExp.test(originalValue)) {\n\t\t\t\tconst ast = valueParser(originalValue).parse();\n\n\t\t\t\t// walk every function in the declaration value\n\t\t\t\tast.walkFunctionNodes(fn => {\n\t\t\t\t\t// if the function is a gradient\n\t\t\t\t\tif (gradientFunctionNameRegExp.test(fn.value)) {\n\t\t\t\t\t\tconst nodes = fn.nodes.slice(1, -1);\n\n\t\t\t\t\t\t// walk every argument to the function\n\t\t\t\t\t\tnodes.forEach((node, index) => {\n\t\t\t\t\t\t\tconst node1back = Object(nodes[index - 1]);\n\t\t\t\t\t\t\tconst node2back = Object(nodes[index - 2]);\n\n\t\t\t\t\t\t\tconst isDoublePositionLength = node2back.type && node1back.type === 'number' && node.type === 'number';\n\n\t\t\t\t\t\t\t// if the argument concludes a double-position gradient\n\t\t\t\t\t\t\tif (isDoublePositionLength) {\n\t\t\t\t\t\t\t\t// insert the fallback colors\n\t\t\t\t\t\t\t\tconst color = node2back.clone();\n\t\t\t\t\t\t\t\tconst comma = valueParser.comma({ value: ',', raws: { after: ' ' } });\n\n\t\t\t\t\t\t\t\tfn.insertBefore(node, comma);\n\t\t\t\t\t\t\t\tfn.insertBefore(node, color);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tconst modifiedValue = ast.toString();\n\n\t\t\t\t// if the value has changed due to double-position gradients\n\t\t\t\tif (originalValue !== modifiedValue) {\n\t\t\t\t\t// add the fallback value\n\t\t\t\t\tdecl.cloneBefore({ value: modifiedValue });\n\n\t\t\t\t\t// conditionally remove the double-position gradient\n\t\t\t\t\tif (!preserve) {\n\t\t\t\t\t\tdecl.remove();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t};\n});\n\nconst gradientFunctionRegExp = /(repeating-)?(conic|linear|radial)-gradient\\([\\W\\w]*\\)/i;\nconst gradientFunctionNameRegExp = /^(repeating-)?(conic|linear|radial)-gradient$/i;\n"], "names": ["postcss", "plugin", "opts", "preserve", "Object", "Boolean", "root", "walkDecls", "decl", "originalValue", "value", "gradientFunctionRegExp", "test", "ast", "valueParser", "parse", "walkFunctionNodes", "fn", "gradientFunctionNameRegExp", "nodes", "slice", "for<PERSON>ach", "node", "index", "node1back", "node2back", "isDoublePositionLength", "type", "color", "clone", "comma", "raws", "after", "insertBefore", "modifiedValue", "toString", "cloneBefore", "remove"], "mappings": ";;;AAGA,YAAeA,OAAO,CAACC,MAAR,CAAe,mCAAf,EAAoDC,IAAI,IAAI;QACpEC,QAAQ,GAAG,cAAcC,MAAM,CAACF,IAAD,CAApB,GAA6BG,OAAO,CAACH,IAAI,CAACC,QAAN,CAApC,GAAsD,IAAvE;SAEOG,IAAI,IAAI;;IAEdA,IAAI,CAACC,SAAL,CAAeC,IAAI,IAAI;YACPC,aADO,GACWD,IADX,CACdE,KADc;;UAIlBC,sBAAsB,CAACC,IAAvB,CAA4BH,aAA5B,CAAJ,EAAgD;cACzCI,GAAG,GAAGC,WAAW,CAACL,aAAD,CAAX,CAA2BM,KAA3B,EAAZ,CAD+C;;QAI/CF,GAAG,CAACG,iBAAJ,CAAsBC,EAAE,IAAI;;cAEvBC,0BAA0B,CAACN,IAA3B,CAAgCK,EAAE,CAACP,KAAnC,CAAJ,EAA+C;kBACxCS,KAAK,GAAGF,EAAE,CAACE,KAAH,CAASC,KAAT,CAAe,CAAf,EAAkB,CAAC,CAAnB,CAAd,CAD8C;;YAI9CD,KAAK,CAACE,OAAN,CAAc,CAACC,IAAD,EAAOC,KAAP,KAAiB;oBACxBC,SAAS,GAAGpB,MAAM,CAACe,KAAK,CAACI,KAAK,GAAG,CAAT,CAAN,CAAxB;oBACME,SAAS,GAAGrB,MAAM,CAACe,KAAK,CAACI,KAAK,GAAG,CAAT,CAAN,CAAxB;oBAEMG,sBAAsB,GAAGD,SAAS,CAACE,IAAV,IAAkBH,SAAS,CAACG,IAAV,KAAmB,QAArC,IAAiDL,IAAI,CAACK,IAAL,KAAc,QAA9F,CAJ8B;;kBAO1BD,sBAAJ,EAA4B;;sBAErBE,KAAK,GAAGH,SAAS,CAACI,KAAV,EAAd;sBACMC,KAAK,GAAGhB,WAAW,CAACgB,KAAZ,CAAkB;kBAAEpB,KAAK,EAAE,GAAT;kBAAcqB,IAAI,EAAE;oBAAEC,KAAK,EAAE;;iBAA/C,CAAd;gBAEAf,EAAE,CAACgB,YAAH,CAAgBX,IAAhB,EAAsBQ,KAAtB;gBACAb,EAAE,CAACgB,YAAH,CAAgBX,IAAhB,EAAsBM,KAAtB;;aAbF;;SANF;cAyBMM,aAAa,GAAGrB,GAAG,CAACsB,QAAJ,EAAtB,CA7B+C;;YAgC3C1B,aAAa,KAAKyB,aAAtB,EAAqC;;UAEpC1B,IAAI,CAAC4B,WAAL,CAAiB;YAAE1B,KAAK,EAAEwB;WAA1B,EAFoC;;cAKhC,CAAC/B,QAAL,EAAe;YACdK,IAAI,CAAC6B,MAAL;;;;KA1CJ;GAFD;CAHc,CAAf;AAuDA,MAAM1B,sBAAsB,GAAG,yDAA/B;AACA,MAAMO,0BAA0B,GAAG,gDAAnC;;;;"}